{"tasks": [{"id": 1, "title": "Setup Project Repository", "description": "Initialize the project directory structure with the specified components and empty files according to EA_Wizard framework requirements.", "status": "done", "dependencies": [], "priority": "high", "details": "Create the `src/` directory and subdirectories (`OnInit/`, `OnTick/`, `OnDeinit/`, `Config/`) as per the EA_Wizard documentation. Add completely empty `index.mqh` files in each subdirectory.", "testStrategy": "Verify directory structure and file existence. Ensure all `index.mqh` files are completely empty.", "subtasks": [{"id": 1, "title": "Create Base Directory Structure", "description": "Create the main project directory and the required subdirectories as per the EA_Wizard documentation.", "dependencies": [], "details": "Create the root project directory. Inside it, create the `src/` directory with the following subdirectories: `OnInit/`, `OnTick/`, `OnDeinit/`, and `Config/` to match the EA_Wizard framework requirements. Ensure all directories are properly named and nested.", "status": "done", "testStrategy": "Verify directory structure exists and matches the specified layout."}, {"id": 2, "title": "Initialize Placeholder Files", "description": "Add completely empty `index.mqh` files in each subdirectory.", "dependencies": [1], "details": "Create an empty `index.mqh` file in each of the subdirectories (`OnInit/`, `OnTick/`, `OnDeinit/`, `Config/`). These files must contain absolutely no content or boilerplate code.", "status": "done", "testStrategy": "Check that each subdirectory contains an empty `index.mqh` file."}, {"id": 4, "title": "Add Basic Documentation Structure", "description": "Create a basic documentation structure following EA_Wizard framework guidelines.", "dependencies": [1], "details": "Create a `docs/` directory in the root project directory. Add a `README.md` file with basic project information and a `CONTRIBUTING.md` file with contribution guidelines.", "status": "done", "testStrategy": "Ensure the `docs/` directory exists and contains the specified files with placeholder content."}]}, {"id": 2, "title": "Implement Core Configuration System", "description": "Setup the basic structure for the configuration system by creating placeholder files.", "status": "pending", "dependencies": [1], "priority": "high", "details": "Create empty `Config.mqh` and `Input.mqh` files in the `Config/` directory with basic file structure and documentation comments. Actual parameter definitions and validation logic will be implemented in separate tasks.", "testStrategy": "Verify files are created in correct location with proper structure and comments.", "subtasks": []}, {"id": 3, "title": "Develop OnInit Module", "description": "Implement the initialization pipeline for the EA.", "details": "Create `OnInit/index.mqh` and supporting modules. Implement initialization logic for technical indicators (Bollinger Bands, MACD, RSI) and risk management settings. Ensure proper error handling.", "testStrategy": "Test initialization of indicators and settings. Verify error handling for invalid inputs.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": [{"id": 1, "title": "Create OnInit directory structure", "description": "Set up the required directory structure for the OnInit module following EA_Wizard framework.", "dependencies": [], "details": "Create the `OnInit` directory and `index.mqh` file as the main entry point.", "status": "pending", "testStrategy": "Verify directory and file creation in the MQL4 environment."}, {"id": 2, "title": "Implement parameter validation", "description": "Add validation logic for input parameters to ensure they are within acceptable ranges.", "dependencies": [1], "details": "Check parameters like lot size, stop loss, take profit, and indicator settings for validity.", "status": "pending", "testStrategy": "Test with invalid parameters to ensure proper error messages."}, {"id": 3, "title": "Initialize Bollinger Bands indicator", "description": "Create and configure the Bollinger Bands indicator handle.", "dependencies": [1], "details": "Use `iBands` function to create the handle and store it for later use.", "status": "pending", "testStrategy": "Verify handle creation and check for errors."}, {"id": 4, "title": "Initialize MACD indicator", "description": "Create and configure the MACD indicator handle.", "dependencies": [1], "details": "Use `iMACD` function to create the handle and store it for later use.", "status": "pending", "testStrategy": "Verify handle creation and check for errors."}, {"id": 5, "title": "Initialize RSI indicator", "description": "Create and configure the RSI indicator handle.", "dependencies": [1], "details": "Use `iRSI` function to create the handle and store it for later use.", "status": "pending", "testStrategy": "Verify handle creation and check for errors."}, {"id": 6, "title": "Set up risk management parameters", "description": "Initialize risk management settings such as stop loss and take profit.", "dependencies": [2], "details": "Calculate and validate risk parameters based on account balance and risk percentage.", "status": "pending", "testStrategy": "Test with different account balances to ensure correct calculations."}, {"id": 7, "title": "Implement error handling mechanism", "description": "Add comprehensive error handling to manage initialization failures.", "dependencies": [3, 4, 5, 6], "details": "Use `GetLastError` to capture and log errors, with fallback mechanisms where possible.", "status": "pending", "testStrategy": "Simulate errors during initialization to verify handling."}, {"id": 8, "title": "Finalize OnInit module integration", "description": "Ensure all components are properly integrated and the module is ready for use.", "dependencies": [7], "details": "Combine all initialized components and perform a final validation check.", "status": "pending", "testStrategy": "Run the EA in a test environment to verify full initialization."}]}, {"id": 4, "title": "Develop OnTick Module", "description": "Implement the tick processing pipeline for real-time trading logic.", "details": "Create `OnTick/index.mqh` and supporting modules. Implement Martingale position scaling logic, entry/exit triggers, and profit calculation. Integrate technical indicator signals (Bollinger Bands, MACD, RSI).", "testStrategy": "Test tick processing logic with simulated market data. Verify position scaling and signal alignment.", "priority": "high", "dependencies": [1, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Develop OnDeinit Mo<PERSON>le", "description": "Implement the cleanup pipeline for resource management.", "details": "Create `OnDeinit/index.mqh` and supporting modules. Implement cleanup logic for closing open positions, releasing resources, and logging shutdown events.", "testStrategy": "Test cleanup functionality by simulating EA shutdown. Verify resource release and logging.", "priority": "medium", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 6, "title": "Implement Martingale Position Management", "description": "Develop the 4-level Martingale position scaling system.", "details": "Implement fixed lot sequence [0.01, 0.01, 0.02, 0.04] with loss-based scaling at 300 points per level. Ensure proper profit calculation (300 points per 0.01 lot).", "testStrategy": "Test position scaling with simulated losses. Verify lot size adjustments and profit calculations.", "priority": "high", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 7, "title": "Integrate Bollinger Bands Indicator", "description": "Implement Bollinger Bands (20-period, 2.0 standard deviation) for signal generation.", "details": "Use MQL4's `iBands` function to calculate Bollinger Bands. Implement overbought/oversold detection logic.", "testStrategy": "Test Bollinger Bands calculations and signal accuracy with historical data.", "priority": "medium", "dependencies": [3, 4], "status": "pending", "subtasks": []}, {"id": 8, "title": "Integrate MACD Indicator", "description": "Implement MACD (12/26/9) for trend direction and momentum confirmation.", "details": "Use MQL4's `iMACD` function to calculate MACD. Implement trend and momentum validation logic.", "testStrategy": "Test MACD calculations and signal alignment with Bollinger Bands.", "priority": "medium", "dependencies": [3, 4], "status": "pending", "subtasks": []}, {"id": 9, "title": "Integrate RSI Indicator", "description": "Implement RSI (14-period, 70/30 thresholds) for additional signal validation.", "details": "Use MQL4's `iRSI` function to calculate RSI. Implement overbought/oversold signal logic.", "testStrategy": "Test RSI calculations and signal alignment with other indicators.", "priority": "medium", "dependencies": [3, 4], "status": "pending", "subtasks": []}, {"id": 10, "title": "Implement Triple Confirmation Signal System", "description": "Develop logic requiring alignment of Bollinger Bands, MACD, and RSI for trade entry.", "details": "Combine signals from all three indicators. Implement validation logic to ensure all indicators align before trade execution.", "testStrategy": "Test signal alignment with simulated market conditions. Verify trade execution only on confirmed signals.", "priority": "high", "dependencies": [7, 8, 9], "status": "pending", "subtasks": []}, {"id": 11, "title": "Implement Account Protection System", "description": "Develop risk management to limit account losses to 20%.", "details": "Monitor account balance and equity in real-time. Implement logic to halt trading if losses exceed 20%.", "testStrategy": "Simulate account losses to verify protection triggers.", "priority": "high", "dependencies": [3, 4], "status": "pending", "subtasks": []}, {"id": 12, "title": "Implement Position and Spread Controls", "description": "Add controls for maximum concurrent orders (20) and spread (5 points).", "details": "Track open orders and spread values. Implement logic to reject trades exceeding limits.", "testStrategy": "Test order and spread limits with simulated market conditions.", "priority": "medium", "dependencies": [3, 4], "status": "pending", "subtasks": []}, {"id": 13, "title": "Develop Real-time Monitoring System", "description": "Implement continuous risk assessment and alerting.", "details": "Monitor account health, open positions, and market conditions. Implement alerts for critical events.", "testStrategy": "Simulate critical events to verify alert generation.", "priority": "medium", "dependencies": [11, 12], "status": "pending", "subtasks": []}, {"id": 14, "title": "Optimize Performance for Real-time Trading", "description": "Ensure the EA processes ticks in under 100ms.", "details": "Profile and optimize tick processing logic. Minimize redundant calculations and improve resource usage.", "testStrategy": "Measure tick processing time under load. Verify performance meets requirements.", "priority": "high", "dependencies": [4, 6, 10], "status": "pending", "subtasks": []}, {"id": 15, "title": "Document and Deploy", "description": "Complete documentation and prepare for production deployment.", "details": "Write technical documentation, user guides, and configuration examples. Package the EA for deployment.", "testStrategy": "Review documentation for completeness. Verify deployment package integrity.", "priority": "medium", "dependencies": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "status": "pending", "subtasks": []}]}