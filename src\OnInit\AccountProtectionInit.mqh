//+------------------------------------------------------------------+
//|                                        AccountProtectionInit.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_INIT_MQH
#define ACCOUNT_PROTECTION_INIT_MQH

#property strict

// Include EA_Wizard framework components
#include "../../mql4_module/EA_Wizard/MainPipeline.mqh"

// Include account protection module
#include "../../module/RiskManagement/AccountProtection/AccountProtection.mqh"

//+------------------------------------------------------------------+
//| Account Protection Input Parameters                              |
//| Self-contained module parameters following EA_Wizard guidelines |
//+------------------------------------------------------------------+

// Protection Level Settings
input ENUM_PROTECTION_LEVEL InpProtectionLevel = PROTECTION_MODERATE;  // Account Protection Level

// Account Protection Limits (will be overridden by protection level if set to 0)
input double InpMaxLossPercent = 20.0;          // Maximum Loss Percentage (0 = use protection level default)
input double InpMaxDailyLoss = 0.0;             // Maximum Daily Loss Amount (0 = auto-calculate)
input double InpMaxDrawdownPercent = 30.0;      // Maximum Drawdown Percentage (0 = use protection level default)

// Position Limits
input int InpMaxOpenOrders = 20;                // Maximum Open Orders (0 = use protection level default)
input double InpMaxLotSize = 2.0;               // Maximum Lot Size per Trade (0 = use protection level default)
input double InpMaxTotalLotSize = 10.0;         // Maximum Total Lot Size (0 = use protection level default)
input double InpMaxSpread = 5.0;                // Maximum Allowed Spread (0 = use protection level default)

// Monitoring Settings
input bool InpEnableAccountProtection = true;   // Enable Account Protection System
input bool InpLogProtectionEvents = true;       // Log Protection Events
input int InpProtectionCheckInterval = 1;       // Protection Check Interval (seconds)

//+------------------------------------------------------------------+
//| Account Protection Constants                                     |
//| Self-contained constants within the module                      |
//+------------------------------------------------------------------+

// Validation Constants
const double MIN_LOSS_PERCENT = 1.0;            // Minimum loss percentage
const double MAX_LOSS_PERCENT = 50.0;           // Maximum loss percentage
const double MIN_DRAWDOWN_PERCENT = 1.0;        // Minimum drawdown percentage
const double MAX_DRAWDOWN_PERCENT = 80.0;       // Maximum drawdown percentage
const int MIN_OPEN_ORDERS = 1;                  // Minimum open orders
const int MAX_OPEN_ORDERS = 100;                // Maximum open orders
const double MIN_LOT_SIZE = 0.01;               // Minimum lot size
const double MAX_LOT_SIZE = 100.0;              // Maximum lot size
const double MIN_SPREAD = 0.0;                  // Minimum spread
const double MAX_SPREAD = 50.0;                 // Maximum spread

// Error Codes
const int ERR_INVALID_PROTECTION_LEVEL = 9001;  // Invalid protection level
const int ERR_INVALID_LOSS_PERCENT = 9002;      // Invalid loss percentage
const int ERR_INVALID_DRAWDOWN_PERCENT = 9003;  // Invalid drawdown percentage
const int ERR_INVALID_OPEN_ORDERS = 9004;       // Invalid open orders count
const int ERR_INVALID_LOT_SIZE = 9005;          // Invalid lot size
const int ERR_INVALID_SPREAD = 9006;            // Invalid spread value
const int ERR_ACCOUNT_PROTECTION_INIT = 9007;   // Account protection initialization failed
const int ERR_INVALID_ACCOUNT_BALANCE = 9008;   // Invalid account balance

//+------------------------------------------------------------------+
//| Global Account Protection Instance                               |
//+------------------------------------------------------------------+
AccountProtection* g_accountProtection = NULL;

//+------------------------------------------------------------------+
//| Account Protection Initialization Pipeline                       |
//| Implements account protection system initialization during      |
//| EA startup following EA_Wizard framework guidelines             |
//+------------------------------------------------------------------+
class AccountProtectionInit : public MainPipeline
{
private:
    double m_initialBalance;        // Captured initial balance
    double m_initialEquity;         // Captured initial equity
    datetime m_initTime;            // Initialization timestamp
    bool m_validationPassed;       // Parameter validation status

public:
    //--- Constructor
    AccountProtectionInit() : MainPipeline(INIT_ENVIRONMENT, "AccountProtectionInit")
    {
        m_initialBalance = 0.0;
        m_initialEquity = 0.0;
        m_initTime = 0;
        m_validationPassed = false;
    }

    //--- Destructor
    ~AccountProtectionInit()
    {
        // Cleanup handled by framework
    }

protected:
    //--- Main initialization logic
    virtual void Main() override
    {
        Print("=== Account Protection Initialization Started ===");
        
        // Step 1: Validate input parameters
        if (!ValidateInputParameters())
        {
            SetResult(false, "Account protection parameter validation failed");
            return;
        }
        
        // Step 2: Capture baseline account metrics
        if (!CaptureBaselineMetrics())
        {
            SetResult(false, "Failed to capture baseline account metrics");
            return;
        }
        
        // Step 3: Initialize account protection system
        if (!InitializeAccountProtection())
        {
            SetResult(false, "Failed to initialize account protection system");
            return;
        }
        
        // Step 4: Register account protection instance
        if (!RegisterAccountProtection())
        {
            SetResult(false, "Failed to register account protection instance");
            return;
        }
        
        // Step 5: Perform final validation
        if (!PerformFinalValidation())
        {
            SetResult(false, "Account protection final validation failed");
            return;
        }
        
        LogInitializationSuccess();
        SetResult(true, "Account protection system initialized successfully");
    }

private:
    //--- Validate all input parameters
    bool ValidateInputParameters()
    {
        Print("Validating account protection parameters...");
        
        // Validate protection level
        if (InpProtectionLevel < PROTECTION_NONE || InpProtectionLevel > PROTECTION_STRICT)
        {
            Print("ERROR: Invalid protection level: ", InpProtectionLevel);
            return false;
        }
        
        // Validate loss percentage (if specified)
        if (InpMaxLossPercent > 0.0 && 
            (InpMaxLossPercent < MIN_LOSS_PERCENT || InpMaxLossPercent > MAX_LOSS_PERCENT))
        {
            Print("ERROR: Invalid max loss percentage: ", InpMaxLossPercent, 
                  " (Range: ", MIN_LOSS_PERCENT, "-", MAX_LOSS_PERCENT, ")");
            return false;
        }
        
        // Validate drawdown percentage (if specified)
        if (InpMaxDrawdownPercent > 0.0 && 
            (InpMaxDrawdownPercent < MIN_DRAWDOWN_PERCENT || InpMaxDrawdownPercent > MAX_DRAWDOWN_PERCENT))
        {
            Print("ERROR: Invalid max drawdown percentage: ", InpMaxDrawdownPercent,
                  " (Range: ", MIN_DRAWDOWN_PERCENT, "-", MAX_DRAWDOWN_PERCENT, ")");
            return false;
        }
        
        // Validate open orders count (if specified)
        if (InpMaxOpenOrders > 0 && 
            (InpMaxOpenOrders < MIN_OPEN_ORDERS || InpMaxOpenOrders > MAX_OPEN_ORDERS))
        {
            Print("ERROR: Invalid max open orders: ", InpMaxOpenOrders,
                  " (Range: ", MIN_OPEN_ORDERS, "-", MAX_OPEN_ORDERS, ")");
            return false;
        }
        
        // Validate lot sizes (if specified)
        if (InpMaxLotSize > 0.0 && 
            (InpMaxLotSize < MIN_LOT_SIZE || InpMaxLotSize > MAX_LOT_SIZE))
        {
            Print("ERROR: Invalid max lot size: ", InpMaxLotSize,
                  " (Range: ", MIN_LOT_SIZE, "-", MAX_LOT_SIZE, ")");
            return false;
        }
        
        if (InpMaxTotalLotSize > 0.0 && 
            (InpMaxTotalLotSize < MIN_LOT_SIZE || InpMaxTotalLotSize > MAX_LOT_SIZE))
        {
            Print("ERROR: Invalid max total lot size: ", InpMaxTotalLotSize,
                  " (Range: ", MIN_LOT_SIZE, "-", MAX_LOT_SIZE, ")");
            return false;
        }
        
        // Validate spread (if specified)
        if (InpMaxSpread > 0.0 && 
            (InpMaxSpread < MIN_SPREAD || InpMaxSpread > MAX_SPREAD))
        {
            Print("ERROR: Invalid max spread: ", InpMaxSpread,
                  " (Range: ", MIN_SPREAD, "-", MAX_SPREAD, ")");
            return false;
        }
        
        m_validationPassed = true;
        Print("Parameter validation completed successfully");
        return true;
    }
    
    //--- Capture baseline account metrics
    bool CaptureBaselineMetrics()
    {
        Print("Capturing baseline account metrics...");
        
        m_initTime = TimeCurrent();
        m_initialBalance = AccountBalance();
        m_initialEquity = AccountEquity();
        
        // Validate account balance
        if (m_initialBalance <= 0.0)
        {
            Print("ERROR: Invalid account balance: ", m_initialBalance);
            return false;
        }
        
        // Validate account equity
        if (m_initialEquity <= 0.0)
        {
            Print("ERROR: Invalid account equity: ", m_initialEquity);
            return false;
        }
        
        Print("Baseline metrics captured:");
        Print("  Initial Balance: ", DoubleToString(m_initialBalance, 2));
        Print("  Initial Equity: ", DoubleToString(m_initialEquity, 2));
        Print("  Initialization Time: ", TimeToString(m_initTime));
        
        return true;
    }
    
    //--- Initialize account protection system
    bool InitializeAccountProtection()
    {
        Print("Initializing account protection system...");
        
        // Check if account protection is enabled
        if (!InpEnableAccountProtection)
        {
            Print("Account protection is disabled by user settings");
            return true;
        }
        
        // Create account protection instance
        g_accountProtection = new AccountProtection(InpProtectionLevel);
        if (g_accountProtection == NULL)
        {
            Print("ERROR: Failed to create account protection instance");
            return false;
        }
        
        // Configure custom parameters if specified
        ConfigureCustomParameters();
        
        // Initialize the account protection system
        if (!g_accountProtection.Initialize())
        {
            Print("ERROR: Account protection initialization failed: ", 
                  g_accountProtection.GetLastErrorMessage());
            delete g_accountProtection;
            g_accountProtection = NULL;
            return false;
        }
        
        Print("Account protection system initialized with level: ", 
              EnumToString(InpProtectionLevel));
        return true;
    }
    
    //--- Configure custom parameters if specified
    void ConfigureCustomParameters()
    {
        if (g_accountProtection == NULL) return;
        
        // Set custom parameters if specified (non-zero values override defaults)
        if (InpMaxLossPercent > 0.0)
            g_accountProtection.SetMaxLossPercent(InpMaxLossPercent);
            
        if (InpMaxDailyLoss > 0.0)
            g_accountProtection.SetMaxDailyLoss(InpMaxDailyLoss);
            
        if (InpMaxDrawdownPercent > 0.0)
            g_accountProtection.SetMaxDrawdownPercent(InpMaxDrawdownPercent);
            
        if (InpMaxOpenOrders > 0)
            g_accountProtection.SetMaxOpenOrders(InpMaxOpenOrders);
            
        if (InpMaxLotSize > 0.0)
            g_accountProtection.SetMaxLotSize(InpMaxLotSize);
            
        if (InpMaxSpread > 0.0)
            g_accountProtection.SetMaxSpread(InpMaxSpread);
    }
    
    //--- Register account protection instance with framework
    bool RegisterAccountProtection()
    {
        if (!InpEnableAccountProtection || g_accountProtection == NULL)
        {
            Print("Account protection registration skipped (disabled or not initialized)");
            return true;
        }
        
        Print("Registering account protection with EA_Wizard framework...");
        
        // Register with the framework's object registry
        if (!Register(g_accountProtection, "AccountProtection", "Account Protection System"))
        {
            Print("ERROR: Failed to register account protection with framework");
            return false;
        }
        
        Print("Account protection registered successfully");
        return true;
    }
    
    //--- Perform final validation of the initialized system
    bool PerformFinalValidation()
    {
        if (!InpEnableAccountProtection)
        {
            Print("Final validation skipped (account protection disabled)");
            return true;
        }
        
        if (g_accountProtection == NULL)
        {
            Print("ERROR: Account protection instance is null");
            return false;
        }
        
        Print("Performing final validation...");
        
        // Validate the initialized system
        if (!g_accountProtection.Validate())
        {
            Print("ERROR: Account protection validation failed: ",
                  g_accountProtection.GetLastErrorMessage());
            return false;
        }
        
        // Check if trading is initially allowed
        bool tradingAllowed = g_accountProtection.IsTradingAllowed();
        Print("Trading allowed status: ", tradingAllowed ? "YES" : "NO");
        
        if (!tradingAllowed)
        {
            Print("WARNING: Trading is not allowed by account protection system");
            Print("Current status: ", g_accountProtection.GetStatusDescription());
        }
        
        Print("Final validation completed successfully");
        return true;
    }
    
    //--- Log successful initialization
    void LogInitializationSuccess()
    {
        if (!InpLogProtectionEvents) return;
        
        Print("=== Account Protection Initialization Summary ===");
        Print("Protection Level: ", EnumToString(InpProtectionLevel));
        Print("Protection Enabled: ", InpEnableAccountProtection ? "YES" : "NO");
        
        if (InpEnableAccountProtection && g_accountProtection != NULL)
        {
            Print("Max Loss Percent: ", DoubleToString(g_accountProtection.GetMaxLossPercent(), 2), "%");
            Print("Current Status: ", g_accountProtection.GetStatusDescription());
            Print("Trading Allowed: ", g_accountProtection.IsTradingAllowed() ? "YES" : "NO");
        }
        
        Print("Initial Balance: ", DoubleToString(m_initialBalance, 2));
        Print("Initial Equity: ", DoubleToString(m_initialEquity, 2));
        Print("Initialization Time: ", TimeToString(m_initTime));
        Print("=== Initialization Complete ===");
    }
};

// Create global instance for automatic registration
AccountProtectionInit account_protection_init_stage;

#endif // ACCOUNT_PROTECTION_INIT_MQH
