//+------------------------------------------------------------------+
//|                                          AccountProtection.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_MQH
#define ACCOUNT_PROTECTION_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Protection Level Enumeration                                     |
//+------------------------------------------------------------------+
enum ENUM_PROTECTION_LEVEL
{
    PROTECTION_NONE = 0,        // No protection
    PROTECTION_BASIC = 1,       // Basic protection
    PROTECTION_MODERATE = 2,    // Moderate protection
    PROTECTION_STRICT = 3       // Strict protection
};

//+------------------------------------------------------------------+
//| Protection Status Enumeration                                    |
//+------------------------------------------------------------------+
enum ENUM_PROTECTION_STATUS
{
    STATUS_NORMAL = 0,          // Normal operation
    STATUS_WARNING = 1,         // Warning level
    STATUS_CRITICAL = 2,        // Critical level
    STATUS_EMERGENCY = 3        // Emergency stop
};

//+------------------------------------------------------------------+
//| AccountProtection Class                                          |
//| Implementation of comprehensive account protection system       |
//+------------------------------------------------------------------+
class AccountProtection : public BaseComponent
{
private:
    ENUM_PROTECTION_LEVEL   m_protectionLevel;     // Protection level
    ENUM_PROTECTION_STATUS  m_currentStatus;       // Current protection status
    
    // Account limits
    double                  m_maxLossPercent;       // Maximum loss percentage
    double                  m_maxDailyLoss;         // Maximum daily loss
    double                  m_maxDrawdownPercent;   // Maximum drawdown percentage
    double                  m_minEquityPercent;     // Minimum equity percentage
    
    // Position limits
    int                     m_maxOpenOrders;        // Maximum open orders
    double                  m_maxLotSize;           // Maximum lot size per trade
    double                  m_maxTotalLotSize;      // Maximum total lot size
    double                  m_maxSpread;            // Maximum allowed spread
    
    // Monitoring variables
    double                  m_initialBalance;       // Initial account balance
    double                  m_dailyStartBalance;    // Balance at start of day
    double                  m_maxEquity;            // Maximum equity reached
    datetime                m_lastResetTime;        // Last daily reset time
    bool                    m_tradingHalted;        // Trading halt flag
    
public:
    //--- Constructor and Destructor
                            AccountProtection(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE);
    virtual                ~AccountProtection();
    
    //--- Configuration methods
    void                    SetProtectionLevel(ENUM_PROTECTION_LEVEL level);
    void                    SetMaxLossPercent(double percent) { m_maxLossPercent = MathMax(1.0, MathMin(50.0, percent)); }
    void                    SetMaxDailyLoss(double amount) { m_maxDailyLoss = MathMax(0.0, amount); }
    void                    SetMaxDrawdownPercent(double percent) { m_maxDrawdownPercent = MathMax(1.0, MathMin(80.0, percent)); }
    void                    SetMaxOpenOrders(int count) { m_maxOpenOrders = MathMax(1, count); }
    void                    SetMaxLotSize(double lotSize) { m_maxLotSize = MathMax(0.01, lotSize); }
    void                    SetMaxSpread(double spread) { m_maxSpread = MathMax(0.0, spread); }
    
    //--- Information methods
    ENUM_PROTECTION_LEVEL   GetProtectionLevel() const { return m_protectionLevel; }
    ENUM_PROTECTION_STATUS  GetCurrentStatus() const { return m_currentStatus; }
    double                  GetMaxLossPercent() const { return m_maxLossPercent; }
    double                  GetCurrentLossPercent() const;
    double                  GetCurrentDrawdownPercent() const;
    bool                    IsTradingHalted() const { return m_tradingHalted; }
    
    //--- Protection check methods
    bool                    IsTradingAllowed();
    bool                    IsNewOrderAllowed(double lotSize = 0.0);
    bool                    IsSpreadAcceptable(string symbol = "");
    bool                    CheckAccountLimits();
    bool                    CheckPositionLimits(double lotSize = 0.0);
    bool                    CheckDailyLimits();
    
    //--- Status update methods
    void                    UpdateStatus();
    void                    ResetDailyCounters();
    void                    HaltTrading(string reason = "");
    void                    ResumeTrading();
    void                    EmergencyStop(string reason = "");
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;
    
    //--- Utility methods
    double                  GetAccountBalance() const;
    double                  GetAccountEquity() const;
    double                  GetAccountProfit() const;
    int                     GetOpenOrdersCount() const;
    double                  GetTotalLotSize() const;
    double                  GetCurrentSpread(string symbol = "") const;
    bool                    IsNewTradingDay() const;
    string                  GetStatusDescription() const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
AccountProtection::AccountProtection(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE) : BaseComponent("AccountProtection")
{
    m_protectionLevel = level;
    m_currentStatus = STATUS_NORMAL;
    
    // Set default limits based on protection level
    SetProtectionLevel(level);
    
    m_initialBalance = 0.0;
    m_dailyStartBalance = 0.0;
    m_maxEquity = 0.0;
    m_lastResetTime = 0;
    m_tradingHalted = false;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountProtection::~AccountProtection()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize account protection                                    |
//+------------------------------------------------------------------+
bool AccountProtection::OnInitialize()
{
    m_initialBalance = GetAccountBalance();
    m_dailyStartBalance = m_initialBalance;
    m_maxEquity = GetAccountEquity();
    m_lastResetTime = TimeCurrent();
    m_tradingHalted = false;
    m_currentStatus = STATUS_NORMAL;
    
    if (m_initialBalance <= 0.0)
    {
        SetError(901, "Invalid initial account balance");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool AccountProtection::OnValidate()
{
    if (m_maxLossPercent <= 0.0 || m_maxLossPercent > 50.0)
    {
        SetError(902, "Invalid maximum loss percentage");
        return false;
    }
    
    if (m_maxOpenOrders <= 0)
    {
        SetError(903, "Invalid maximum open orders count");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update protection status                                         |
//+------------------------------------------------------------------+
bool AccountProtection::OnUpdate()
{
    // Check if new trading day
    if (IsNewTradingDay())
    {
        ResetDailyCounters();
    }
    
    // Update maximum equity
    double currentEquity = GetAccountEquity();
    if (currentEquity > m_maxEquity)
    {
        m_maxEquity = currentEquity;
    }
    
    // Update protection status
    UpdateStatus();
    
    return true;
}

//+------------------------------------------------------------------+
//| Set protection level                                             |
//+------------------------------------------------------------------+
void AccountProtection::SetProtectionLevel(ENUM_PROTECTION_LEVEL level)
{
    m_protectionLevel = level;
    
    switch(level)
    {
        case PROTECTION_NONE:
            m_maxLossPercent = 50.0;
            m_maxDrawdownPercent = 80.0;
            m_maxOpenOrders = 100;
            m_maxLotSize = 10.0;
            m_maxTotalLotSize = 50.0;
            m_maxSpread = 20.0;
            break;
            
        case PROTECTION_BASIC:
            m_maxLossPercent = 30.0;
            m_maxDrawdownPercent = 50.0;
            m_maxOpenOrders = 50;
            m_maxLotSize = 5.0;
            m_maxTotalLotSize = 20.0;
            m_maxSpread = 10.0;
            break;
            
        case PROTECTION_MODERATE:
            m_maxLossPercent = 20.0;
            m_maxDrawdownPercent = 30.0;
            m_maxOpenOrders = 20;
            m_maxLotSize = 2.0;
            m_maxTotalLotSize = 10.0;
            m_maxSpread = 5.0;
            break;
            
        case PROTECTION_STRICT:
            m_maxLossPercent = 10.0;
            m_maxDrawdownPercent = 15.0;
            m_maxOpenOrders = 10;
            m_maxLotSize = 1.0;
            m_maxTotalLotSize = 5.0;
            m_maxSpread = 3.0;
            break;
    }
    
    m_minEquityPercent = 100.0 - m_maxLossPercent;
    m_maxDailyLoss = m_initialBalance * (m_maxLossPercent / 2.0) / 100.0; // Half of max loss for daily limit
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool AccountProtection::IsTradingAllowed()
{
    if (m_tradingHalted)
        return false;
    
    if (m_currentStatus == STATUS_EMERGENCY)
        return false;
    
    return CheckAccountLimits() && CheckDailyLimits();
}

//+------------------------------------------------------------------+
//| Check if new order is allowed                                    |
//+------------------------------------------------------------------+
bool AccountProtection::IsNewOrderAllowed(double lotSize = 0.0)
{
    if (!IsTradingAllowed())
        return false;
    
    return CheckPositionLimits(lotSize) && IsSpreadAcceptable();
}

//+------------------------------------------------------------------+
//| Check if spread is acceptable                                    |
//+------------------------------------------------------------------+
bool AccountProtection::IsSpreadAcceptable(string symbol = "")
{
    double currentSpread = GetCurrentSpread(symbol);
    return (currentSpread <= m_maxSpread);
}

//+------------------------------------------------------------------+
//| Check account limits                                             |
//+------------------------------------------------------------------+
bool AccountProtection::CheckAccountLimits()
{
    double currentLossPercent = GetCurrentLossPercent();
    double currentDrawdownPercent = GetCurrentDrawdownPercent();
    
    if (currentLossPercent >= m_maxLossPercent)
    {
        EmergencyStop("Maximum loss percentage exceeded");
        return false;
    }
    
    if (currentDrawdownPercent >= m_maxDrawdownPercent)
    {
        EmergencyStop("Maximum drawdown percentage exceeded");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check position limits                                            |
//+------------------------------------------------------------------+
bool AccountProtection::CheckPositionLimits(double lotSize = 0.0)
{
    if (GetOpenOrdersCount() >= m_maxOpenOrders)
        return false;
    
    if (lotSize > m_maxLotSize)
        return false;
    
    if (GetTotalLotSize() + lotSize > m_maxTotalLotSize)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check daily limits                                               |
//+------------------------------------------------------------------+
bool AccountProtection::CheckDailyLimits()
{
    if (m_maxDailyLoss <= 0.0)
        return true;
    
    double dailyLoss = m_dailyStartBalance - GetAccountBalance();
    return (dailyLoss < m_maxDailyLoss);
}

//+------------------------------------------------------------------+
//| Update protection status                                         |
//+------------------------------------------------------------------+
void AccountProtection::UpdateStatus()
{
    double lossPercent = GetCurrentLossPercent();
    double drawdownPercent = GetCurrentDrawdownPercent();
    
    if (lossPercent >= m_maxLossPercent * 0.9 || drawdownPercent >= m_maxDrawdownPercent * 0.9)
    {
        m_currentStatus = STATUS_EMERGENCY;
    }
    else if (lossPercent >= m_maxLossPercent * 0.7 || drawdownPercent >= m_maxDrawdownPercent * 0.7)
    {
        m_currentStatus = STATUS_CRITICAL;
    }
    else if (lossPercent >= m_maxLossPercent * 0.5 || drawdownPercent >= m_maxDrawdownPercent * 0.5)
    {
        m_currentStatus = STATUS_WARNING;
    }
    else
    {
        m_currentStatus = STATUS_NORMAL;
    }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                             |
//+------------------------------------------------------------------+
void AccountProtection::ResetDailyCounters()
{
    m_dailyStartBalance = GetAccountBalance();
    m_lastResetTime = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Halt trading                                                     |
//+------------------------------------------------------------------+
void AccountProtection::HaltTrading(string reason = "")
{
    m_tradingHalted = true;
    Print("TRADING HALTED: ", reason);
}

//+------------------------------------------------------------------+
//| Resume trading                                                   |
//+------------------------------------------------------------------+
void AccountProtection::ResumeTrading()
{
    m_tradingHalted = false;
    Print("TRADING RESUMED");
}

//+------------------------------------------------------------------+
//| Emergency stop                                                   |
//+------------------------------------------------------------------+
void AccountProtection::EmergencyStop(string reason = "")
{
    m_currentStatus = STATUS_EMERGENCY;
    HaltTrading("EMERGENCY: " + reason);
}

//+------------------------------------------------------------------+
//| Get current loss percentage                                      |
//+------------------------------------------------------------------+
double AccountProtection::GetCurrentLossPercent() const
{
    if (m_initialBalance <= 0.0)
        return 0.0;
    
    double currentBalance = GetAccountBalance();
    double loss = m_initialBalance - currentBalance;
    return (loss / m_initialBalance) * 100.0;
}

//+------------------------------------------------------------------+
//| Get current drawdown percentage                                  |
//+------------------------------------------------------------------+
double AccountProtection::GetCurrentDrawdownPercent() const
{
    if (m_maxEquity <= 0.0)
        return 0.0;
    
    double currentEquity = GetAccountEquity();
    double drawdown = m_maxEquity - currentEquity;
    return (drawdown / m_maxEquity) * 100.0;
}

//+------------------------------------------------------------------+
//| Get account balance                                              |
//+------------------------------------------------------------------+
double AccountProtection::GetAccountBalance() const
{
    return AccountBalance();
}

//+------------------------------------------------------------------+
//| Get account equity                                               |
//+------------------------------------------------------------------+
double AccountProtection::GetAccountEquity() const
{
    return AccountEquity();
}

//+------------------------------------------------------------------+
//| Get account profit                                               |
//+------------------------------------------------------------------+
double AccountProtection::GetAccountProfit() const
{
    return AccountProfit();
}

//+------------------------------------------------------------------+
//| Get open orders count                                            |
//+------------------------------------------------------------------+
int AccountProtection::GetOpenOrdersCount() const
{
    return OrdersTotal();
}

//+------------------------------------------------------------------+
//| Get total lot size                                               |
//+------------------------------------------------------------------+
double AccountProtection::GetTotalLotSize() const
{
    double totalLots = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            totalLots += OrderLots();
        }
    }
    
    return totalLots;
}

//+------------------------------------------------------------------+
//| Get current spread                                               |
//+------------------------------------------------------------------+
double AccountProtection::GetCurrentSpread(string symbol = "") const
{
    if (symbol == "")
        symbol = Symbol();
    
    return MarketInfo(symbol, MODE_SPREAD);
}

//+------------------------------------------------------------------+
//| Check if new trading day                                         |
//+------------------------------------------------------------------+
bool AccountProtection::IsNewTradingDay() const
{
    datetime currentTime = TimeCurrent();
    datetime lastTime = m_lastResetTime;
    
    return (TimeDay(currentTime) != TimeDay(lastTime) || 
            TimeMonth(currentTime) != TimeMonth(lastTime) ||
            TimeYear(currentTime) != TimeYear(lastTime));
}

//+------------------------------------------------------------------+
//| Get status description                                           |
//+------------------------------------------------------------------+
string AccountProtection::GetStatusDescription() const
{
    switch(m_currentStatus)
    {
        case STATUS_NORMAL:     return "Normal";
        case STATUS_WARNING:    return "Warning";
        case STATUS_CRITICAL:   return "Critical";
        case STATUS_EMERGENCY:  return "Emergency";
        default:                return "Unknown";
    }
}

#endif // ACCOUNT_PROTECTION_MQH
