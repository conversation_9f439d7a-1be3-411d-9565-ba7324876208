//+------------------------------------------------------------------+
//|                                                        Index.mqh |
//|                                            EA_Wizard Framework  |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef CONFIG_INDEX_MQH
#define CONFIG_INDEX_MQH

//+------------------------------------------------------------------+
//| Configuration System Unified Entry Point                        |
//+------------------------------------------------------------------+
//| This file serves as the unified entry point for all             |
//| configuration-related files in the Config directory.            |
//|                                                                  |
//| Purpose:                                                         |
//| - Provide single include point for all configuration files      |
//| - Manage dependencies between configuration components          |
//| - Ensure proper initialization order of configuration system    |
//|                                                                  |
//| Usage:                                                           |
//| Simply include this file to access all configuration            |
//| functionality:                                                   |
//|   #include "Config/Index.mqh"                                    |
//|                                                                  |
//| Future Includes:                                                 |
//| - Config.mqh: Core configuration management system              |
//| - Input.mqh: External input parameter definitions               |
//+------------------------------------------------------------------+

// TODO: Include Config.mqh when implementation is ready
// #include "Config.mqh"

// TODO: Include Input.mqh when implementation is ready
// #include "Input.mqh"

#endif // CONFIG_INDEX_MQH