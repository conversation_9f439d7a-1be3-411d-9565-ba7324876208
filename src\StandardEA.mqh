//+------------------------------------------------------------------+
//| StandardEA.mqh                                                   |
//| EA_Wizard-dev2 Main Expert Advisor File                         |
//| Copyright 2024, EA_Wizard Framework                              |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, EA_Wizard Framework"
#property link      ""
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| Main Expert Advisor File                                         |
//| This file serves as the main entry point for the EA_Wizard-dev2 |
//| Expert Advisor, implementing the standardized EA_Wizard         |
//| framework architecture                                           |
//+------------------------------------------------------------------+

// Include EA_Wizard framework components
#include "../EA_Wizard/TradingController.mqh"
#include "../EA_Wizard/MainPipeline.mqh"

// Include project modules
#include "Config/index.mqh"
#include "OnInit/index.mqh"
#include "OnTick/index.mqh"
#include "OnDeinit/index.mqh"

//+------------------------------------------------------------------+
//| EA_Wizard-dev2 Main Class                                       |
//| Implements the main EA logic using EA_Wizard framework          |
//+------------------------------------------------------------------+
class EA_Wizard_dev2
{
private:
    TradingController* m_controller;
    bool m_initialized;
    bool m_active;

public:
    //+------------------------------------------------------------------+
    //| Constructor                                                      |
    //+------------------------------------------------------------------+
    EA_Wizard_dev2()
    {
        m_controller = NULL;
        m_initialized = false;
        m_active = false;
    }

    //+------------------------------------------------------------------+
    //| Destructor                                                       |
    //+------------------------------------------------------------------+
    ~EA_Wizard_dev2()
    {
        Cleanup();
    }

    //+------------------------------------------------------------------+
    //| Initialize the EA                                                |
    //+------------------------------------------------------------------+
    int Initialize()
    {
        Print("EA_Wizard-dev2: Starting Expert Advisor initialization...");

        // Initialize TradingController
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        if(driver == NULL)
        {
            Print("ERROR: Failed to get TradingPipelineDriver instance");
            return INIT_FAILED;
        }

        m_controller = new TradingController(driver, "EA_Wizard-dev2");
        if(m_controller == NULL)
        {
            Print("ERROR: Failed to create TradingController");
            return INIT_FAILED;
        }

        // Initialize configuration system
        if(!InitializeConfigManager())
        {
            Print("ERROR: Configuration system initialization failed");
            return INIT_FAILED;
        }

        // Initialize OnInit pipeline
        int initResult = InitializeOnInitPipeline();
        if(initResult != INIT_SUCCEEDED)
        {
            Print("ERROR: OnInit pipeline initialization failed");
            return initResult;
        }

        // Initialize OnTick pipeline
        if(!InitializeOnTickPipeline())
        {
            Print("ERROR: OnTick pipeline initialization failed");
            return INIT_FAILED;
        }

        // Initialize OnDeinit pipeline
        if(!InitializeOnDeinitPipeline())
        {
            Print("ERROR: OnDeinit pipeline initialization failed");
            return INIT_FAILED;
        }

        // Execute controller initialization
        int controllerResult = m_controller.OnInit();
        if(controllerResult != INIT_SUCCEEDED)
        {
            Print("ERROR: TradingController initialization failed");
            return controllerResult;
        }

        m_initialized = true;
        m_active = true;

        Print("EA_Wizard-dev2: Expert Advisor initialized successfully");
        Print("EA_Wizard-dev2: Ready for trading on ", Symbol());

        return INIT_SUCCEEDED;
    }

    //+------------------------------------------------------------------+
    //| Process tick events                                              |
    //+------------------------------------------------------------------+
    void OnTick()
    {
        if(!m_initialized || !m_active)
        {
            return;
        }

        // Execute controller tick processing
        if(m_controller != NULL)
        {
            m_controller.OnTick();
        }

        // Process OnTick pipeline
        ProcessOnTickPipeline();
    }

    //+------------------------------------------------------------------+
    //| Process deinitialization                                         |
    //+------------------------------------------------------------------+
    void OnDeinit(const int reason)
    {
        Print("EA_Wizard-dev2: Deinitialization started, reason: ", reason);

        m_active = false;

        // Process OnDeinit pipeline
        ProcessOnDeinitPipeline(reason);

        // Execute controller deinitialization
        if(m_controller != NULL)
        {
            m_controller.OnDeinit(reason);
        }

        // Cleanup all resources
        Cleanup();

        Print("EA_Wizard-dev2: Deinitialization completed");
    }

    //+------------------------------------------------------------------+
    //| Cleanup all resources                                            |
    //+------------------------------------------------------------------+
    void Cleanup()
    {
        // Cleanup pipelines
        CleanupOnDeinitPipeline();
        CleanupOnTickPipeline();
        CleanupOnInitPipeline();
        CleanupConfigManager();

        // Cleanup controller
        if(m_controller != NULL)
        {
            delete m_controller;
            m_controller = NULL;
        }

        m_initialized = false;
        m_active = false;
    }

    //+------------------------------------------------------------------+
    //| Check if EA is initialized                                       |
    //+------------------------------------------------------------------+
    bool IsInitialized() const
    {
        return m_initialized;
    }

    //+------------------------------------------------------------------+
    //| Check if EA is active                                            |
    //+------------------------------------------------------------------+
    bool IsActive() const
    {
        return m_active;
    }

    //+------------------------------------------------------------------+
    //| Set EA active state                                              |
    //+------------------------------------------------------------------+
    void SetActive(bool active)
    {
        m_active = active;
        Print("EA_Wizard-dev2: Active state set to ", (active ? "TRUE" : "FALSE"));
    }
};

// Global EA instance
EA_Wizard_dev2* g_ea = NULL;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("=== EA_Wizard-dev2 Expert Advisor Starting ===");

    // Create EA instance
    if(g_ea != NULL)
    {
        delete g_ea;
    }

    g_ea = new EA_Wizard_dev2();
    if(g_ea == NULL)
    {
        Print("FATAL ERROR: Failed to create EA instance");
        return INIT_FAILED;
    }

    // Initialize EA
    int result = g_ea.Initialize();
    if(result != INIT_SUCCEEDED)
    {
        Print("FATAL ERROR: EA initialization failed");
        delete g_ea;
        g_ea = NULL;
        return result;
    }

    Print("=== EA_Wizard-dev2 Expert Advisor Started Successfully ===");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    if(g_ea != NULL && g_ea.IsInitialized())
    {
        g_ea.OnTick();
    }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== EA_Wizard-dev2 Expert Advisor Stopping ===");

    if(g_ea != NULL)
    {
        g_ea.OnDeinit(reason);
        delete g_ea;
        g_ea = NULL;
    }

    Print("=== EA_Wizard-dev2 Expert Advisor Stopped ===");
}
